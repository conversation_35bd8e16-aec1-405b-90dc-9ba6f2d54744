// Variables - g<PERSON><PERSON> nguyên từ Product.scss
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$accent-color: #ff5a5a;
$blue: #007bff;
$transition: all 0.3s ease;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

// Mixins
@mixin navy-gradient {
  background: linear-gradient(135deg, $navy-dark, $navy-medium, $navy-light);
  background-size: 200% 200%;
  animation: gradientMove 8s ease infinite;
}

@mixin hover-transform {
  transition: $transition;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }
}

.productdetail-container {
  display: flex;
  flex-wrap: wrap;
  max-width: 1200px;
  max-height: 80vh;
  min-height: 80vh;
  margin: 30px auto;
  padding: 0 20px;
  overflow-y: scroll;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 400px;

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid $light-gray;
      border-top: 5px solid $navy-medium;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    p {
      color: $navy-dark;
      font-size: 16px;
      font-weight: 500;
    }
  }
  // Phần hình ảnh sản phẩm (bên trái)
  .productdetail-image-container {
    flex: 1;
    min-width: 300px;
    padding: 20px;
    border: 1px solid $medium-gray;
    border-radius: $border-radius;
    background-color: $white;
    margin-right: 30px;
    box-shadow: $box-shadow;
    position: relative;
    overflow: hidden;

    // Navy gradient strip at top
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      @include navy-gradient;
    }

    .productdetail-image {
      width: 100%;
      height: auto;
      max-height: 500px;
      object-fit: contain;
      transition: $transition;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  // Phần thông tin sản phẩm (bên phải)
  .productdetail-info {
    flex: 1;
    min-width: 300px;
    padding: 25px;
    border: 1px solid $medium-gray;
    border-radius: $border-radius;
    background-color: $white;
    box-shadow: $box-shadow;
    position: relative;

    // Navy gradient strip at top
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      @include navy-gradient;
    }

    .productdetail-name {
      font-size: 24px;
      font-weight: 700;
      color: $navy-dark;
      margin-bottom: 20px;
      position: relative;
      padding-bottom: 15px;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        height: 2px;
        width: 60px;
        @include navy-gradient;
        border-radius: 2px;
      }

      &.limit-text-to-2-lines {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 80px;
      }
    }

    .productdetail-rating-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .productdetail-rating-count {
        color: $navy-light;
        font-weight: 500;

        &.link-primary {
          cursor: pointer;
          transition: $transition;

          &:hover {
            color: $accent-color;
            text-decoration: underline;
          }
        }
      }
    }

    .productdetail-price {
      font-size: 28px;
      font-weight: 700;
      color: $accent-color;
      margin-bottom: 25px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .original-price {
        text-decoration: line-through;
        color: $dark-gray;
        font-size: 18px;
        font-weight: 400;
        margin-bottom: 5px;
      }

      .sale-price {
        color: $accent-color;
        font-size: 28px;
        font-weight: 700;
      }

      &::before {
        content: "";
        position: absolute;
        bottom: -5px;
        left: 0;
        right: 0;
        height: 1px;
        background-color: rgba($accent-color, 0.3);
      }
    }

    .productdetail-shop {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      padding: 8px 15px;
      background-color: $light-gray;
      border-radius: 20px;
      width: fit-content;

      i {
        color: $navy-medium;
        font-size: 16px;
      }

      span {
        color: $navy-dark;
        font-size: 15px;
        font-weight: 500;
      }
    }

    .productdetail-stock {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;

      i {
        color: $navy-medium;
        font-size: 16px;
      }

      span {
        color: $navy-dark;
        font-size: 15px;
        font-weight: 500;
      }
    }

    .productdetail-description {
      color: $dark-gray;
      line-height: 1.6;
      margin-bottom: 25px;
      padding-bottom: 20px;
      border-bottom: 1px solid $medium-gray;
    }

    .productdetail-quantity-container {
      margin-bottom: 25px;
      display: flex;
      align-items: center;

      label {
        margin-right: 15px;
        font-weight: 600;
        color: $navy-dark;
      }

      .select-container {
        padding: 10px 15px;
        border: 1px solid $navy-light;
        border-radius: $border-radius;
        background-color: $white;
        color: $navy-dark;
        font-weight: 500;
        transition: $transition;
        cursor: pointer;
        min-width: 80px;

        &:focus {
          outline: none;
          border-color: $navy-dark;
          box-shadow: 0 0 0 3px rgba($navy-light, 0.15);
        }

        &:hover {
          border-color: $navy-dark;
        }
      }
    }

    .variant-details {
      background-color: $light-gray;
      border-radius: $border-radius;
      padding: 15px;
      margin-bottom: 20px;

      .variant-sku {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 10px;

        .label {
          font-weight: 600;
          color: $navy-dark;
        }

        .value {
          color: $navy-medium;
          font-weight: 500;
        }
      }

      .variant-attributes {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .attribute-item {
          background-color: $white;
          padding: 6px 12px;
          border-radius: 4px;
          border: 1px solid $medium-gray;
          display: flex;
          align-items: center;
          gap: 5px;

          .attribute-key {
            font-weight: 600;
            color: $navy-dark;
            font-size: 13px;
          }

          .attribute-value {
            color: $navy-medium;
            font-size: 13px;
          }
        }
      }
    }

    .product-attributes-container {
      margin-bottom: 25px;

      .attributes-title {
        font-weight: 600;
        color: $navy-dark;
        margin-bottom: 12px;
      }

      .attributes-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .product-attributes-button {
          padding: 8px 15px;
          background-color: $light-gray;
          border: 1px solid $medium-gray;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          color: $navy-dark;
          transition: $transition;

          &:hover {
            background-color: darken($light-gray, 5%);
          }

          &.selected {
            background-color: $navy-light;
            color: $white;
            border-color: $navy-light;
          }
        }
      }
    }

    .added-to-cart {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .productdetail-checkmark {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .productdetail-added {
        color: #4caf50;
        font-weight: 600;
      }
    }

    .addToCart-button {
      background-color: $navy-medium;
      color: $white;
      border: none;
      padding: 14px 25px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      border-radius: $border-radius;
      transition: $transition;
      position: relative;
      overflow: hidden;
      display: inline-block;
      text-align: center;
      min-width: 200px;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
      }

      &:hover {
        background-color: $navy-dark;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba($navy-dark, 0.3);

        &::before {
          transform: translateX(100%);
        }
      }

      &:active {
        transform: translateY(0);
      }

      &.button-primary {
        background-color: $navy-medium;
      }
    }
  }

  // Phần thông tin chi tiết sản phẩm (phía dưới)
  .productdetail-tabs {
    width: 100%;
    margin-top: 30px;
    border: 1px solid $medium-gray;
    border-radius: $border-radius;
    background-color: $white;
    box-shadow: $box-shadow;
    overflow: hidden;

    .tabs-header {
      display: flex;
      border-bottom: 1px solid $medium-gray;

      .tab-button {
        padding: 15px 25px;
        background: none;
        border: none;
        cursor: pointer;
        font-weight: 600;
        color: $navy-dark;
        transition: $transition;
        position: relative;

        &.active {
          color: $navy-light;

          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            @include navy-gradient;
          }
        }

        &:hover:not(.active) {
          background-color: $light-gray;
        }
      }
    }

    .tab-content {
      padding: 25px;

      &:not(.active) {
        display: none;
      }

      h3 {
        color: $navy-dark;
        margin-bottom: 15px;
        font-size: 18px;
      }

      p {
        color: $dark-gray;
        line-height: 1.6;
        margin-bottom: 15px;
      }

      ul {
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          color: $dark-gray;
        }
      }
    }
  }
}

// Animations
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Media Queries
@media (max-width: 992px) {
  .productdetail-container {
    .productdetail-image-container {
      margin-right: 0;
      margin-bottom: 20px;
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .productdetail-container {
    padding: 0 15px;

    .productdetail-info {
      padding: 20px;

      .productdetail-name {
        font-size: 22px;
      }

      .productdetail-price {
        font-size: 24px;
      }

      .addToCart-button {
        width: 100%;
      }
    }

    .productdetail-tabs {
      .tabs-header {
        flex-wrap: wrap;

        .tab-button {
          padding: 12px 15px;
          font-size: 14px;
        }
      }

      .tab-content {
        padding: 20px;
      }
    }
  }
}

@media (max-width: 480px) {
  .productdetail-container {
    .productdetail-info {
      padding: 15px;

      .productdetail-name {
        font-size: 20px;
      }

      .productdetail-price {
        font-size: 22px;
      }

      .productdetail-quantity-container {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 10px;
        }
      }
    }
  }
}
