@import "../../styles/common.scss";

.register-seller-container {
    padding: 20px;
    max-width: 900px;
    margin: 0 auto;

    .register-seller-content {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 30px;
        position: relative;
        overflow: hidden;

        // Navy gradient strip at top
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #1a3a6c, #2c4272, #3a5998);
            background-size: 200% 200%;
            animation: gradientMove 8s ease infinite;
        }

        .title {
            text-align: center;
            margin: 0 0 30px;
            color: #1a3a6c;
            font-size: 28px;
            position: relative;
            padding-bottom: 15px;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 80px;
                height: 3px;
                background: linear-gradient(135deg, #1a3a6c, #2c4272, #3a5998);
                border-radius: 3px;
            }
        }

        // Styles for shop status container
        .shop-status-container {
            padding: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
            margin-bottom: 30px;

            .status-pending {
                text-align: center;
                padding: 30px 20px;

                i {
                    font-size: 60px;
                    color: #f39c12;
                    margin-bottom: 20px;
                    display: block;
                }

                h2 {
                    color: #333;
                    margin-bottom: 15px;
                    font-size: 24px;
                }

                p {
                    color: #666;
                    font-size: 16px;
                    max-width: 500px;
                    margin: 0 auto;
                }
            }

            .status-rejected {
                text-align: center;
                padding: 30px 20px;

                i {
                    font-size: 60px;
                    color: #e74c3c;
                    margin-bottom: 20px;
                    display: block;
                }

                h2 {
                    color: #333;
                    margin-bottom: 15px;
                    font-size: 24px;
                }

                p {
                    color: #666;
                    font-size: 16px;
                    max-width: 500px;
                    margin: 0 auto 20px;
                }

                .rejection-reason {
                    background-color: #fff;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 0 auto 25px;
                    max-width: 500px;
                    text-align: left;

                    h3 {
                        color: #333;
                        font-size: 18px;
                        margin-bottom: 10px;
                    }

                    p {
                        margin: 0;
                    }
                }

                .btn-reapply {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 5px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: background-color 0.3s;

                    &:hover {
                        background-color: #2980b9;
                    }
                }
            }

            .status-accepted {
                text-align: center;
                padding: 30px 20px;

                i {
                    font-size: 60px;
                    color: #2ecc71;
                    margin-bottom: 20px;
                    display: block;
                }

                h2 {
                    color: #333;
                    margin-bottom: 15px;
                    font-size: 24px;
                }

                p {
                    color: #666;
                    font-size: 16px;
                    max-width: 500px;
                    margin: 0 auto 25px;
                }

                .btn-manage-shop {
                    background-color: #2ecc71;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 5px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: background-color 0.3s;

                    &:hover {
                        background-color: #27ae60;
                    }
                }
            }
        }

        .register-form {
            .form-row {
                display: flex;
                gap: 20px;
                margin-bottom: 15px;

                .col-6 {
                    flex: 0 0 calc(50% - 10px);
                }
            }

            .form-group {
                margin-bottom: 20px;

                label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: 500;
                    color: #333;
                }

                input, textarea {
                    width: 100%;
                    padding: 12px 15px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    font-size: 15px;
                    transition: border-color 0.3s;

                    &:focus {
                        border-color: #1a3a6c;
                        outline: none;
                    }
                }

                textarea {
                    resize: vertical;
                }

                .upload-image {
                    display: flex;
                    gap: 20px;
                    align-items: center;

                    .upload-button {
                        display: inline-block;
                        padding: 12px 20px;
                        background-color: #1a3a6c;
                        color: white;
                        border-radius: 5px;
                        cursor: pointer;
                        transition: background-color 0.3s;

                        &:hover {
                            background-color: adjust-color(#1a3a6c, $lightness: -10%);
                        }

                        i {
                            margin-right: 8px;
                        }
                    }

                    .preview-image {
                        flex: 1;
                        height: 150px;
                        border: 1px dashed #ddd;
                        border-radius: 5px;
                        overflow: hidden;

                        .preview {
                            width: 100%;
                            height: 100%;
                            background-size: contain;
                            background-position: center;
                            background-repeat: no-repeat;
                        }

                        .no-preview {
                            width: 100%;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            color: #999;

                            i {
                                font-size: 40px;
                                margin-bottom: 10px;
                            }
                        }
                    }
                }
            }

            .error-message {
                background-color: #ffebee;
                color: #e53935;
                padding: 12px 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                display: flex;
                align-items: center;

                i {
                    margin-right: 10px;
                    font-size: 18px;
                }
            }

            .form-actions {
                text-align: center;
                margin-top: 30px;

                .btn-submit {
                    padding: 12px 30px;
                    background-color: #4caf50;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background-color 0.3s;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;

                    &:hover {
                        background-color: adjust-color(#4caf50, $lightness: -10%);
                    }

                    &:disabled {
                        background-color: #a5d6a7;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}

@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
