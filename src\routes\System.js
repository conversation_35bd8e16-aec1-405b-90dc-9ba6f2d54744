import React, { Component } from "react";
import { connect } from "react-redux";
import { Redirect, Route, Switch } from "react-router-dom";
import UserManage from "../containers/System/UserManage";
import UserDetail from "../containers/System/UserDetail";
import UserAddress from "../containers/System/UserAddress";

import ShopAdmin from "../containers/System/ShopAdmin";
import ItemManage from "../containers/System/ItemManage";
import ShippingMethodPage from "../containers/System/ShippingMethodPage/ShippingMethodPage";

class System extends Component {
  render() {
    const { systemMenuPath } = this.props;
    return (
      <div className="system-container">
        <div className="system-list">
          <Switch>
            <Route path="/system/user-manage" component={UserManage} />
            <Route path="/system/user-detail" component={UserDetail} />
            <Route path="/system/user-address" component={UserAddress} />
            <Route path="/system/item-manage" component={ItemManage} />
            <Route path="/system/shop-admin" component={ShopAdmin} />
            <Route path="/system/shipping-method" component={ShippingMethodPage} />
            <Route
              component={() => {
                return <Redirect to={systemMenuPath} />;
              }}
            />
          </Switch>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    systemMenuPath: state.app.systemMenuPath,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(System);
