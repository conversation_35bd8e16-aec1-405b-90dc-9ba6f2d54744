/* UserManage.scss - Modern Version */

// Variables
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$orange: #ff9800;
$red: #e53935;
$transition: all 0.3s ease;
$border-radius: 8px;

// Mixins
@mixin navy-gradient {
  background: linear-gradient(135deg, $navy-dark, $navy-medium, $navy-light);
  background-size: 200% 200%;
  animation: gradientMove 8s ease infinite;
}

@mixin star-effect {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(1px 1px at 10% 20%, rgba(255,255,255,0.6) 50%, transparent 100%),
      radial-gradient(1.2px 1.2px at 70% 30%, rgba(255,255,255,0.5) 50%, transparent 100%),
      radial-gradient(1px 1px at 30% 70%, rgba(255,255,255,0.6) 50%, transparent 100%),
      radial-gradient(1.3px 1.3px at 80% 60%, rgba(255,255,255,0.5) 50%, transparent 100%),
      radial-gradient(1.1px 1.1px at 50% 40%, rgba(255,255,255,0.6) 50%, transparent 100%);
    pointer-events: none;
    opacity: 0.3;
    animation: twinkle 5s infinite alternate;
  }
}

@mixin button-style {
  padding: 8px 14px;
  border: none;
  outline: none;
  cursor: pointer;
  border-radius: $border-radius;
  transition: $transition;
}

.users-container {
  margin: 0 20px;
  background-color: $white;
  border-radius: $border-radius;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 25px;
  position: relative;
  overflow: hidden;

  // Navy gradient strip at top
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    @include navy-gradient;
    @include star-effect;
  }

  .title {
    text-align: center;
    margin: 5px 0 25px;
    text-transform: uppercase;
    font-size: 22px;
    font-weight: 600;
    color: $navy-dark;
    position: relative;
    padding-bottom: 12px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 3px;
      width: 70px;
      @include navy-gradient;
      border-radius: 3px;
    }
  }

  .btn-edit {
    @include button-style;
    background-color: rgba($orange, 0.1);
    color: $orange;

    &:hover {
      background-color: rgba($orange, 0.2) !important;
      color: $orange !important;
      transform: translateY(-2px);
      box-shadow: 0 3px 8px rgba($orange, 0.3);
    }
  }

  .btn-delete {
    @include button-style;
    background-color: rgba($red, 0.1);
    color: $red;

    &:hover {
      background-color: rgba($red, 0.2) !important;
      color: $red !important;
      transform: translateY(-2px);
      box-shadow: 0 3px 8px rgba($red, 0.3);
    }
  }

  #customers {
    font-family: 'Segoe UI', Arial, sans-serif;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .user-image {
      transition: $transition;
      border: 2px solid transparent;

      &:hover {
        transform: scale(1.1);
        border-color: $navy-light;
        box-shadow: 0 0 8px rgba($navy-light, 0.5);
      }
    }

    td, th {
      padding: 14px 16px;
      text-align: left;
    }

    th {
      @include navy-gradient;
      @include star-effect;
      color: $white;
      font-weight: 500;
      text-transform: uppercase;
      font-size: 13px;
      letter-spacing: 0.5px;
    }

    tr {
      transition: $transition;

      td {
        border-bottom: 1px solid $medium-gray;
      }

      &:nth-child(even) {
        background-color: $light-gray;
      }

      &:hover {
        background-color: rgba($navy-light, 0.05);
      }

      &:last-child td {
        border-bottom: none;
      }
    }
  }
}

.modal-user-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  

  .modal-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    
    max-height: 90vh;
    position: relative;
    top: -50px; /* Đưa modal cao lên một chút */

    .modal-header {
      @include navy-gradient;
      @include star-effect;
      color: $white;
      padding: 16px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      button {
        background-color: transparent !important;
        color: $white;
        border: none;
        font-size: 24px;
        opacity: 0.8;
        transition: $transition;

        &:hover {
          opacity: 1;
          transform: rotate(90deg);
        }
      }
    }
  }

  .modal-user-body {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;

    .input-container {
      display: flex;
      flex-direction: column;
      width: calc(50% - 10px);

      &:last-child {
        width: 100%;
      }

      label {
        margin-bottom: 8px;
        font-weight: 500;
        color: $navy-dark;
        font-size: 14px;
      }

      input, select {
        border-radius: $border-radius;
        border: 1px solid $medium-gray;
        height: 42px;
        padding: 0 15px;
        transition: $transition;

        &:focus {
          border-color: $navy-medium;
          box-shadow: 0 0 0 3px rgba($navy-light, 0.15);
          outline: none;
        }
      }

      .required {
        color: $red;
        margin-left: 3px;
      }

      .preview-img-container {
        display: flex;
        flex-direction: column;

        .label-upload {
          padding: 8px 12px;
          border-radius: $border-radius;
          background-color: #f0f0f0;
          border: 1px dashed $navy-light;
          cursor: pointer;
          display: inline-block;
          color: $navy-dark;
          font-weight: 500;
          text-align: center;
          margin-bottom: 10px;
          transition: $transition;

          &:hover {
            background-color: rgba($navy-light, 0.1);
          }

          i {
            margin-left: 5px;
          }
        }

        .preview-image {
          height: 150px;
          width: 100%;
          border: 1px solid $medium-gray;
          background: #f8f8f8 url('https://via.placeholder.com/150?text=No+Image') center center no-repeat;
          background-size: contain;
          cursor: pointer;
          border-radius: $border-radius;
          transition: $transition;

          &:hover {
            border-color: $navy-light;
            box-shadow: 0 0 0 3px rgba($navy-light, 0.15);
          }
        }
      }
    }
  }
}

// Modern Confirm Modal
.confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1060;
  animation: backdropFadeIn 0.3s ease-out;

  .modal-content {
    position: relative;
    top: -50px; /* Đưa modal cao lên một chút */
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.25);
    animation: modalFadeIn 0.3s ease-out;
    border: none;
  }

  .modal-header {
    background: linear-gradient(135deg, $navy-dark, $navy-medium);
    color: white;
    padding: 18px 24px;
    border-bottom: none;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(1px 1px at 10% 20%, rgba(255,255,255,0.4) 50%, transparent 100%),
        radial-gradient(1px 1px at 70% 50%, rgba(255,255,255,0.3) 50%, transparent 100%),
        radial-gradient(1px 1px at 30% 70%, rgba(255,255,255,0.4) 50%, transparent 100%);
      pointer-events: none;
      opacity: 0.2;
    }

    .modal-title {
      font-size: 20px;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
        font-size: 22px;
      }
    }

    .close {
      color: white;
      opacity: 0.8;
      font-size: 24px;
      padding: 0;
      margin: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transition: all 0.2s;

      &:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.2);
        transform: rotate(90deg);
      }
    }
  }

  .modal-body {
    padding: 24px;
    background-color: white;

    p {
      font-size: 16px;
      color: $dark-gray;
      margin-bottom: 20px;

      strong {
        color: $navy-dark;
        font-weight: 600;
      }
    }

    .user-info-preview {
      display: flex;
      align-items: center;
      background-color: $light-gray;
      padding: 15px;
      border-radius: 10px;
      margin-top: 20px;
      border: 1px solid $medium-gray;

      img {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid white;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        margin-right: 15px;
      }

      .user-info-text {
        p {
          margin: 0 0 8px;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            display: inline-block;
            width: 70px;
          }
        }
      }
    }
  }

  .modal-footer {
    padding: 16px 24px;
    background-color: $light-gray;
    border-top: 1px solid $medium-gray;
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    button {
      padding: 10px 20px;
      border-radius: 8px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
      min-width: 100px;

      i {
        margin-right: 8px;
      }

      &.btn-secondary {
        background-color: white;
        color: $dark-gray;
        border: 1px solid $medium-gray;

        &:hover {
          background-color: #f1f1f1;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, $navy-dark, $navy-medium);
        border: none;
        color: white;

        &:hover {
          background: linear-gradient(135deg, darken($navy-dark, 5%), darken($navy-medium, 5%));
          box-shadow: 0 4px 10px rgba($navy-dark, 0.3);
          transform: translateY(-2px);
        }
      }

      &.btn-danger {
        background: linear-gradient(135deg, $red, darken($red, 10%));
        border: none;
        color: white;

        &:hover {
          background: linear-gradient(135deg, darken($red, 5%), darken($red, 15%));
          box-shadow: 0 4px 10px rgba($red, 0.3);
          transform: translateY(-2px);
        }
      }
    }
  }
}

.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  animation: backdropFadeIn 0.3s ease-out;
}

// Animations
@keyframes twinkle {
  0% { opacity: 0.15; }
  50% { opacity: 0.3; }
  100% { opacity: 0.15; }
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translate(-50%, -60%); }
  to { opacity: 1; transform: translate(-50%, -50%); }
}

@keyframes backdropFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeInOut {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.3; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
}

.user-manage-confirm-modal {
  .modal-dialog {
    @media (min-width: 766px) {
      min-width: 650px;
    }
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 32, 0.15);
  }

  .modal-content {
    border: none;
    border-radius: 8px;
  }

  .modal-header {
    background-color: #1a3a6c;
    color: white;
    padding: 16px 20px;
    border-bottom: 1px solid #2d5296;

    .modal-title {
      font-weight: 600;
      font-size: 18px;
    }

    .close {
      color: white;
      opacity: 0.8;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-body {
    padding: 20px;
    background-color: #f8faff;
  }

  hr {
    border: none;
    border-bottom: 1px solid #e0e7f5;
    width: calc(100% - 30px);
    margin: 15px 15px 15px 15px;
  }

  .btn-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;

    .btn {
      width: 120px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:first-child {
        margin-right: 12px;
      }

      &.btn-primary {
        background-color: #1a3a6c;
        border-color: #1a3a6c;

        &:hover,
        &:focus {
          background-color: #2d5296;
          border-color: #2d5296;
        }
      }

      &.btn-secondary {
        background-color: #ffffff;
        border-color: #dee2e6;
        color: #495057;

        &:hover,
        &:focus {
          background-color: #f1f3f5;
          border-color: #ced4da;
        }
      }
    }
  }

  .custom-form-group {
    margin: 10px 0;

    label {
      font-weight: 500;
      color: #1a3a6c;
      margin-bottom: 5px;
    }

    .custom-form-control {
      border: 1px solid #ced4da;
      border-radius: 4px;
      padding: 8px 12px;
      transition: border-color 0.15s ease-in-out;

      &:focus {
        border-color: #4a90e2;
        box-shadow: 0 0 0 0.2rem rgba(26, 58, 108, 0.25);
      }

      &.readonly {
        min-height: 28px;
        height: auto;
        background-color: #f1f3f7;
        border-color: #e0e7f5;
      }
    }
  }

  .modal-footer {
    background-color: #f8faff;
    border-top: 1px solid #e0e7f5;
    padding: 15px 20px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;

  .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(26, 58, 108, 0.1);
      border-radius: 50%;
      border-top-color: #1a3a6c;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 15px;
  }

  p {
      color: #666;
      font-size: 16px;
  }
}