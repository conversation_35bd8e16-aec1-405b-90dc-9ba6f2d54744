@import "../styles/common.scss";

.verify-otp-background {
    background: linear-gradient(
        to right,
        #000428 0%,    
        #004e92 100%   
    );
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.verify-otp-background::before,
.verify-otp-background::after {
    content: "";
    position: absolute;
    width: 300%;
    height: 300%;
    pointer-events: none;
}

.verify-otp-background::before {
    background: 
        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.4) 1%, transparent 3%),
        radial-gradient(circle at 80% 70%, rgba(255,255,255,0.3) 1%, transparent 3%);
    background-size: 150px 150px;
    animation: stars 80s linear infinite;
}

.verify-otp-background::after {
    background: 
        radial-gradient(circle at 50% 20%, rgba(255,255,255,0.25) 0.5%, transparent 2%),
        radial-gradient(circle at 10% 90%, rgba(255,255,255,0.2) 0.5%, transparent 2%);
    background-size: 200px 200px;
    animation: stars 120s linear infinite reverse;
}

.verify-otp-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    width: 550px;
    max-width: 700px;
    transition: transform 0.3s ease;
}

.text-verify-otp-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.text-verify-otp {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
}

.verify-otp-instruction {
    font-size: 1.0rem;
    color: #34495e;
    margin-bottom: 10px;
}

.verify-otp-input {
    margin-bottom: 25px;
    text-align: left;
}

.verify-otp-input label {
    display: block;
    margin-bottom: 8px;
    color: #34495e;
    font-weight: 600;
    font-size: 1.0rem;
}

.verify-otp-input input {
    width: 100%;
    padding: 15px;
    font-size: 1.2rem;
    letter-spacing: 5px;
    text-align: center;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.verify-otp-input input:focus {
    border-color: #3498db;
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.2);
    outline: none;
}

.btn-verify-otp {
    background: linear-gradient(45deg, #000080, #3498db);
    color: white;
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-verify-otp:hover {
    background: linear-gradient(45deg, #3498db, #000080);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.resend-otp-link {
    color: #7f8c8d;
    font-size: 1.0rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.resend-otp-link a {
    color: #3498db;
    text-decoration: none;
    margin-left: 5px;
}

.resend-otp-link a:hover {
    text-decoration: underline;
}

@media (max-width: 480px) {
    .verify-otp-container {
        padding: 30px 20px;
        margin: 15px;
    }
    
    .text-verify-otp {
        font-size: 1.8rem;
    }
}
