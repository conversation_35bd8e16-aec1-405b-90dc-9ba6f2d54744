.shipping-method-container {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px;

  .title {
    font-size: 24px;
    font-weight: 700;
    color: #1a2a36;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #cccccc;
  }

  .search-and-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;

    .search-form {
      display: flex;
      flex: 1;
      max-width: 500px;

      input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #cccccc;
        border-radius: 4px 0 0 4px;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: #55778b;
        }
      }

      button {
        background-color: #33505f;
        color: #ffffff;
        border: none;
        padding: 10px 20px;
        border-radius: 0 4px 4px 0;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: #1a2a36;
        }
      }
    }

    .filter {
      select {
        padding: 10px 15px;
        border: 1px solid #cccccc;
        border-radius: 4px;
        font-size: 14px;
        background-color: #ffffff;
        cursor: pointer;

        &:focus {
          outline: none;
          border-color: #55778b;
        }
      }
    }

    .add-btn {
      background-color: #28a745;
      color: #ffffff;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 12px;
      }

      &:hover {
        background-color: #239639;
        transform: translateY(-2px);
      }
    }
  }

  .shipping-methods-table {
    overflow-x: auto;

    table {
      width: 100%;
      border-collapse: collapse;

      th, td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #f5f5f5;
      }

      th {
        background-color: #f5f5f5;
        color: #1a2a36;
        font-weight: 600;
        white-space: nowrap;
      }

      tr:hover {
        background-color: rgba(245, 245, 245, 0.5);
      }

      .status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;

        &.active {
          background-color: rgba(40, 167, 69, 0.2);
          color: #28a745;
        }

        &.inactive {
          background-color: rgba(220, 53, 69, 0.2);
          color: #dc3545;
        }
      }

      .actions {
        display: flex;
        gap: 10px;

        button {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }

          i {
            font-size: 14px;
          }
        }

        .edit-btn {
          background-color: #55778b;
          color: #ffffff;

          &:hover {
            background-color: #33505f;
          }
        }

        .toggle-btn {
          &.activate {
            background-color: #28a745;
            color: #ffffff;

            &:hover {
              background-color: #239639;
            }
          }

          &.deactivate {
            background-color: #fd7e14;
            color: #ffffff;

            &:hover {
              background-color: #e06612;
            }
          }
        }

        .delete-btn {
          background-color: #dc3545;
          color: #ffffff;

          &:hover {
            background-color: #b02a37;
          }
        }
      }

      .no-data {
        text-align: center;
        color: #555555;
        padding: 30px;
      }
    }
  }

  /* Custom pagination */
  .custom-pagination {
    margin: 30px 0 0;

    ul {
      display: flex;
      justify-content: center;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin: 0 5px;

        button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 4px;
          border: 1px solid #cccccc;
          background-color: #ffffff;
          color: #1a2a36;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            background-color: #f5f5f5;
          }

          &:disabled {
            color: #cccccc;
            cursor: not-allowed;
          }
        }

        &.active button {
          background-color: #33505f;
          color: #ffffff;
          border-color: #33505f;
        }

        &.disabled button {
          color: #cccccc;
          cursor: not-allowed;

          &:hover {
            background-color: transparent;
          }
        }

        &.ellipsis {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          color: #555555;
        }
      }
    }
  }

  /* Modal styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #ffffff;
      border-radius: 4px;
      width: 100%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #f5f5f5;

        h2 {
          font-size: 20px;
          font-weight: 600;
          color: #1a2a36;
          margin: 0;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 20px;
          color: #555555;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            color: #dc3545;
          }
        }
      }

      .modal-body {
        padding: 20px;

        .form-group {
          margin-bottom: 20px;

          label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1a2a36;
          }

          input[type="text"],
          input[type="number"],
          textarea {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #cccccc;
            border-radius: 4px;
            font-size: 14px;

            &:focus {
              outline: none;
              border-color: #55778b;
            }
          }

          textarea {
            min-height: 100px;
            resize: vertical;
          }

          &.checkbox {
            display: flex;
            align-items: center;
            gap: 10px;

            input[type="checkbox"] {
              width: 18px;
              height: 18px;
              cursor: pointer;
            }

            label {
              margin-bottom: 0;
              cursor: pointer;
            }
          }
        }

        .form-row {
          display: flex;
          gap: 20px;

          .form-group {
            flex: 1;
          }
        }
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        padding: 20px;
        border-top: 1px solid #f5f5f5;

        button {
          padding: 10px 20px;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }
        }

        .cancel-btn {
          background-color: #ffffff;
          border: 1px solid #cccccc;
          color: #555555;

          &:hover {
            background-color: #f5f5f5;
          }
        }

        .save-btn {
          background-color: #33505f;
          border: none;
          color: #ffffff;

          &:hover {
            background-color: #1a2a36;
          }
        }
      }
    }
  }
}

/* Custom loading overlay */
.custom-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f5f5f5;
    border-top: 5px solid #33505f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  .loading-text {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* Custom confirm dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1500;

  .confirm-dialog {
    background-color: #ffffff;
    border-radius: 4px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

    .confirm-dialog-header {
      padding: 15px 20px;
      border-bottom: 1px solid #f5f5f5;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1a2a36;
      }
    }

    .confirm-dialog-body {
      padding: 20px;

      p {
        margin: 0;
        color: #555555;
        font-size: 16px;
      }
    }

    .confirm-dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding: 15px 20px;
      border-top: 1px solid #f5f5f5;

      button {
        padding: 8px 15px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }
      }

      .cancel-btn {
        background-color: #ffffff;
        border: 1px solid #cccccc;
        color: #555555;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      .confirm-btn {
        background-color: #dc3545;
        border: none;
        color: #ffffff;

        &:hover {
          background-color: #b02a37;
        }
      }
    }
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .shipping-method-container {
    margin: 10px;
    padding: 15px;

    .search-and-filter {
      flex-direction: column;
      align-items: stretch;

      .search-form {
        max-width: 100%;
      }
    }

    .form-row {
      flex-direction: column;
      gap: 10px;
    }
  }
}
