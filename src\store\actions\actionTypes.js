const actionTypes = Object.freeze({
  //app
  APP_START_UP_COMPLETE: "APP_START_UP_COMPLETE",
  SET_CONTENT_OF_CONFIRM_MODAL: "SET_CONTENT_OF_CONFIRM_MODAL",

  //admin
  ADMIN_LOGIN_SUCCESS: "ADMIN_LOGIN_SUCCESS",
  ADMIN_LOGIN_FAIL: "ADMIN_LOGIN_FAIL",
  PROCESS_LOGOUT: "PROCESS_LOGOUT",

  //user
  FETCH_USERS_START: "FETCH_USERS_START",
  FETCH_USERS_SUCCESS: "FETCH_USERS_SUCCESS",
  FETCH_USERS_FAILED: "FETCH_USERS_FAILED",
  CREATE_USER_SUCCESS: "CREATE_USER_SUCCESS",
  CREATE_USER_FAILED: "CREATE_USER_FAILED",
  UPDATE_USER_SUCCESS: "UPDATE_USER_SUCCESS",
  UPDATE_USER_FAILED: "UPDATE_USER_FAILED",
  UPDATE_USER_INFO : 'UPDATE_USER_INFO',
  DELETE_USER_SUCCESS: "DELETE_USER_SUCCESS",
  DELETE_USER_FAILED: "DELETE_USER_FAILED",
  //userDetail
  FETCH_USER_DETAIL_START: "FETCH_USER_DETAIL_START",
  FETCH_USER_DETAIL_SUCCESS: "FETCH_USER_DETAIL_SUCCESS",
  FETCH_USER_DETAIL_FAILURE: "FETCH_USER_DETAIL_FAILURE",
  UPDATE_USER_DETAIL_SUCCESS: "UPDATE_USER_DETAIL_SUCCESS",
  UPDATE_USER_DETAIL_FAILURE: "UPDATE_USER_DETAIL_FAILURE",
  // Địa chỉ người dùng
  FETCH_USER_ADDRESSES_SUCCESS: "FETCH_USER_ADDRESSES_SUCCESS",
  FETCH_USER_ADDRESSES_FAILURE: "FETCH_USER_ADDRESSES_FAILURE",
  ADD_USER_ADDRESS_SUCCESS: "ADD_USER_ADDRESS_SUCCESS",
  ADD_USER_ADDRESS_FAILURE: "ADD_USER_ADDRESS_FAILURE",

  UPDATE_USER_ADDRESS_SUCCESS: "UPDATE_USER_ADDRESS_SUCCESS",
  UPDATE_USER_ADDRESS_FAILURE: "UPDATE_USER_ADDRESS_FAILURE",

  DELETE_USER_ADDRESS_SUCCESS: "DELETE_USER_ADDRESS_SUCCESS",
  DELETE_USER_ADDRESS_FAILURE: "DELETE_USER_ADDRESS_FAILURE",

  SET_DEFAULT_ADDRESS_SUCCESS: "SET_DEFAULT_ADDRESS_SUCCESS",
  SET_DEFAULT_ADDRESS_FAILURE: "SET_DEFAULT_ADDRESS_FAILURE",
  //navbar and cart
  ADD_TO_CART: "ADD_TO_CART",
  SEARCH_ACTION: "SEARCH_ACTION",
  REMOVE_FROM_CART: "REMOVE_FROM_CART",
  REMOVE_ALL_CART: "REMOVE_ALL_CART",
  UPDATE_CART: "UPDATE_CART",
  UPDATE_QUANTITY: "UPDATE_QUANTITY",
  FETCH_CART: "FETCH_CART",
  //product
  FETCH_PRODUCT: "FETCH_PRODUCT",
  ADD_PRODUCT: "ADD_PRODUCT",
  REMOVE_PRODUCT: "REMOVE_PRODUCT",
  //order
  MAKE_ORDER: "MAKE_ORDER",
  FETCH_ORDER: "FETCH_ORDER",
  // Shop actions
  FETCH_SHOPS_START: "FETCH_SHOPS_START",
  FETCH_SHOPS_SUCCESS: "FETCH_SHOPS_SUCCESS",
  FETCH_SHOPS_FAILED: "FETCH_SHOPS_FAILED",
  CREATE_SHOP_SUCCESS: "CREATE_SHOP_SUCCESS",
  CREATE_SHOP_FAILED: "CREATE_SHOP_FAILED",
  UPDATE_SHOP_SUCCESS: "UPDATE_SHOP_SUCCESS",
  UPDATE_SHOP_FAILED: "UPDATE_SHOP_FAILED",
  DELETE_SHOP_SUCCESS: "DELETE_SHOP_SUCCESS",
  DELETE_SHOP_FAILED: "DELETE_SHOP_FAILED",
  APPROVE_SHOP_SUCCESS: "APPROVE_SHOP_SUCCESS",
  APPROVE_SHOP_FAILED: "APPROVE_SHOP_FAILED",
  REJECT_SHOP_SUCCESS: "REJECT_SHOP_SUCCESS",
  REJECT_SHOP_FAILED: "REJECT_SHOP_FAILED",
});

export default actionTypes;
