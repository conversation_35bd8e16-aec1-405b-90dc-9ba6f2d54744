.seller-orders-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
  min-height: calc(100vh - 200px);

  .seller-orders-header {
    margin-bottom: 30px;

    h1 {
      font-size: 28px;
      font-weight: 700;
      color: #0a1f44;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #dddddd;
      border-top: 5px solid #1a3f6d;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    p {
      font-size: 16px;
      color: #0a1f44;
    }

    @keyframes spin {
      0%   { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    &.modal-loading {
      padding: 30px 0;
      background: none;
      box-shadow: none;
    }
  }

  .no-orders {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    i {
      font-size: 60px;
      color: #888888;
      margin-bottom: 20px;
    }

    p {
      font-size: 18px;
      color: #0a1f44;
      margin-bottom: 30px;
    }
  }

  .orders-list {
    margin-bottom: 30px;

    .orders-table {
      background-color: #ffffff;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .table-header {
        display: flex;
        background-color: #3b5f8f;
        padding: 15px;
        font-weight: 600;
        color: #0a1f44;

        .header-cell {
          padding: 0 10px;

          &.order-id   { width: 12%; }
          &.customer   { width: 15%; }
          &.date       { width: 18%; }
          &.items      { width: 20%; }
          &.total      { width: 12%; }
          &.status     { width: 13%; }
          &.actions    { width: 10%; text-align: center; }
        }
      }

      .table-body {
        .table-row {
          display: flex;
          padding: 15px;
          border-bottom: 1px solid #dddddd;
          align-items: center;

          &:last-child { border-bottom: none; }
          &:hover      { background-color: rgba(221, 221, 221, 0.5); }

          .cell {
            padding: 0 10px;

            &.order-id {
              width: 12%;
              font-weight: 600;
              color: #0a1f44;
            }

            &.customer {
              width: 15%;
            }

            &.date {
              width: 18%;
              font-size: 14px;
              color: #555555;
            }

            &.items {
              width: 20%;

              .items-preview {
                display: flex;
                align-items: center;

                img {
                  width: 40px;
                  height: 40px;
                  object-fit: cover;
                  border-radius: 4px;
                  margin-right: 10px;
                }

                span {
                  font-size: 14px;
                  color: #1a3f6d;
                }
              }
            }

            &.total {
              width: 12%;
              font-weight: 600;
              color: #ff5722;
            }

            &.status {
              width: 13%;

              .status-badge {
                display: inline-block;
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;

                &.status-pending {
                  background-color: rgba(241, 196, 15, 0.1);
                  color: #f1c40f;
                }
                &.status-processing {
                  background-color: rgba(52, 152, 219, 0.1);
                  color: #3498db;
                }
                &.status-shipped {
                  background-color: rgba(155, 89, 182, 0.1);
                  color: #9b59b6;
                }
                &.status-delivered {
                  background-color: rgba(46, 204, 113, 0.1);
                  color: #2ecc71;
                }
                &.status-canceled {
                  background-color: rgba(231, 76, 60, 0.1);
                  color: #e74c3c;
                }
                &.status-unknown {
                  background-color: rgba(85, 85, 85, 0.1);
                  color: #555555;
                }
              }
            }

            &.actions {
              width: 10%;
              text-align: center;

              .view-btn {
                background-color: #3b5f8f;
                color: #0a1f44;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s ease;

                i {
                  margin-right: 5px;
                }

                &:hover {
                  background-color: #1a3f6d;
                  color: #ffffff;
                }
              }
            }
          }
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding: 0;
    margin: 30px 0;

    .page-item {
      margin: 0 5px;
      background: none;
      border: 1px solid #888888;
      border-radius: 4px;
      padding: 0;
      cursor: pointer;

      &.active {
        background-color: #1a3f6d;
        border-color: #1a3f6d;

        .page-link {
          color: #ffffff;
        }
      }
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        color: #0a1f44;
        text-decoration: none;
        transition: all 0.3s ease;
      }
      &:hover:not(:disabled):not(.active) {
        background-color: #dddddd;
        border-color: #3b5f8f;
      }
    }
  }

  .order-details-modal {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-overlay {
      position: absolute;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background-color: rgba(10, 31, 68, 0.7);
    }

    .modal-content {
      position: relative;
      width: 90%;
      max-width: 900px;
      max-height: 90vh;
      background-color: #ffffff;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      z-index: 1001;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background-color: #3b5f8f;
        border-bottom: 1px solid #888888;

        h2 {
          font-size: 20px;
          font-weight: 700;
          color: #0a1f44;
          margin: 0;
        }
        .close-btn {
          background: none;
          border: none;
          color: #0a1f44;
          font-size: 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover { color: #e74c3c; }
        }
      }

      .modal-body {
        padding: 20px;
        max-height: calc(90vh - 70px);
        overflow-y: auto;

        .order-status-section {
          background-color: #ffffff;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          padding: 20px;
          margin-bottom: 20px;

          h3 {
            font-size: 18px;
            font-weight: 600;
            color: #0a1f44;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dddddd;
          }

          .current-status {
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            .status-label {
              margin-right: 10px;
              font-weight: 500;
            }

            .status-badge {
              display: inline-block;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 14px;
              font-weight: 500;

              &.status-pending {
                background-color: rgba(241, 196, 15, 0.1);
                color: #f1c40f;
              }
              &.status-processing {
                background-color: rgba(52, 152, 219, 0.1);
                color: #3498db;
              }
              &.status-shipped {
                background-color: rgba(155, 89, 182, 0.1);
                color: #9b59b6;
              }
              &.status-delivered {
                background-color: rgba(46, 204, 113, 0.1);
                color: #2ecc71;
              }
              &.status-canceled {
                background-color: rgba(231, 76, 60, 0.1);
                color: #e74c3c;
              }
            }
          }

          .status-actions {
            h4 {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 10px;
            }
            .status-buttons {
              display: flex;
              gap: 10px;
              flex-wrap: wrap;

              .status-btn {
                padding: 10px 15px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;

                &.processing {
                  background-color: rgba(52, 152, 219, 0.1);
                  color: #3498db;
                  border: 1px solid #3498db;
                  &:hover {
                    background-color: #3498db;
                    color: #ffffff;
                  }
                }
                &.shipped {
                  background-color: rgba(155, 89, 182, 0.1);
                  color: #9b59b6;
                  border: 1px solid #9b59b6;
                  &:hover {
                    background-color: #9b59b6;
                    color: #ffffff;
                  }
                }
                &.delivered {
                  background-color: rgba(46, 204, 113, 0.1);
                  color: #2ecc71;
                  border: 1px solid #2ecc71;
                  &:hover {
                    background-color: #2ecc71;
                    color: #ffffff;
                  }
                }
                &.canceled {
                  background-color: rgba(231, 76, 60, 0.1);
                  color: #e74c3c;
                  border: 1px solid #e74c3c;
                  &:hover {
                    background-color: #e74c3c;
                    color: #ffffff;
                  }
                }
              }
            }
          }
        }

        .order-details {
          display: flex;
          flex-direction: column;
          gap: 20px;

          .order-info,
          .order-items,
          .shipping-info,
          .order-note {
            background-color: #ffffff;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;

            h3 {
              font-size: 18px;
              font-weight: 600;
              color: #0a1f44;
              margin-bottom: 15px;
              padding-bottom: 10px;
              border-bottom: 1px solid #dddddd;
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;

            .info-item {
              display: flex;
              flex-direction: column;

              .info-label {
                font-size: 14px;
                color: #555555;
                margin-bottom: 5px;
              }
              .info-value {
                font-size: 16px;
                font-weight: 500;
                color: #0a1f44;
              }
            }
          }

          .items-list {
            display: flex;
            flex-direction: column;
            gap: 15px;

            .item {
              display: flex;
              align-items: center;
              padding: 15px;
              border-radius: 6px;
              background-color: #ffffff;
              border: 1px solid #dddddd;

              .item-image {
                width: 60px;
                height: 60px;
                margin-right: 15px;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  border-radius: 4px;
                }
              }

              .item-details {
                flex: 1;

                .item-name {
                  font-weight: 500;
                  color: #0a1f44;
                  margin-bottom: 5px;
                }
                .item-attributes {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 8px;

                  .attribute-item {
                    background-color: #dddddd;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #1a3f6d;
                  }
                }
              }

              .item-price {
                width: 120px;
                text-align: right;
                margin-right: 15px;
                font-size: 14px;
                color: #1a3f6d;
              }

              .item-total {
                width: 100px;
                text-align: right;
                font-weight: 600;
                color: #0a1f44;
              }
            }
          }

          .shipping-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;

            @media (max-width: 768px) {
              grid-template-columns: 1fr;
            }

            h4 {
              font-size: 16px;
              font-weight: 600;
              color: #0a1f44;
              margin-bottom: 10px;
            }

            .address-info,
            .method-info {
              background-color: #dddddd;
              padding: 15px;
              border-radius: 6px;

              p {
                margin-bottom: 5px;
                &:last-child {
                  margin-bottom: 0;
                }
              }

              .address-type,
              .method-name {
                font-weight: 500;
                color: #0a1f44;
              }
              .method-cost {
                font-weight: 600;
                color: #ff5722;
              }
            }
          }

          .order-note {
            p {
              padding: 15px;
              background-color: #dddddd;
              border-radius: 6px;
              color: #0a1f44;
              font-style: italic;
            }
          }
        }
      }
    }
  }
}
