@use 'sass:color';
@import "common";


.custom-form-group {
  margin-bottom: 16px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1a3a6c;
  }

  .custom-form-control {
    display: block;
    width: 100%;
    height: 40px;
    padding: 0 12px;
    line-height: 38px;
    border-radius: 8px;
    background-color: $common-white;
    border: 1px solid #dbe1e9;
    transition: all 0.2s ease;
    font-size: 15px;
    
    &:hover:not(:disabled):not(.readonly) {
      border-color: #4a7ab8;
    }
    
    &.readonly {
      cursor: not-allowed;
      background-color: color.adjust($common-white, $lightness: -5%);
      color: #7a8699;
    }
    
    &:focus {
      outline: none;
      border-color: #2d5296;
      box-shadow: 0 0 0 3px rgba(45, 82, 150, 0.2);
    }
    
    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
      background-color: #f8fafc;
    }
  }

  select.custom-form-control {
    padding-right: 34px;
    background-image: $dropdown-image;
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 14px;
    cursor: pointer;

    &:disabled {
      cursor: not-allowed;
    }
  }

  textarea.custom-form-control {
    min-height: 80px;
    height: auto;
    line-height: 1.5;
    padding: 12px;
    resize: vertical;
  }

  .custom-checkbox-control {
    display: flex;
    align-items: center;
    min-height: 32px;
    
    input {
      cursor: pointer;
      width: 18px;
      height: 18px;
      margin-right: 10px;
      accent-color: #1a3a6c;
    }

    label {
      margin: 0;
      cursor: pointer;
    }
  }
}
