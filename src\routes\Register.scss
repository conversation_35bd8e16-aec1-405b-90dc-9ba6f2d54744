@import "../styles/common.scss";

.register-background {
    background: linear-gradient(
        to right,
        #000428 0%,    
        #004e92 100%   
    );
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.register-background::before,
.register-background::after {
    content: "";
    position: absolute;
    width: 300%;
    height: 300%;
    pointer-events: none;
}

.register-background::before {
    background: 
        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.4) 1%, transparent 3%),
        radial-gradient(circle at 80% 70%, rgba(255,255,255,0.3) 1%, transparent 3%);
    background-size: 150px 150px;
    animation: stars 80s linear infinite;
}

.register-background::after {
    background: 
        radial-gradient(circle at 50% 20%, rgba(255,255,255,0.25) 0.5%, transparent 2%),
        radial-gradient(circle at 10% 90%, rgba(255,255,255,0.2) 0.5%, transparent 2%);
    background-size: 200px 200px;
    animation: stars 120s linear infinite reverse;
}

.register-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    width: 550px;
    max-width: 700px;
    transition: transform 0.3s ease;
}

.text-register-container {
    display: flex;
    justify-content: center;
    align-items: center;
     
}

.text-register {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
}

.register-input {
    margin-bottom: 25px;
    text-align: left;
}

.register-input label {
    display: block;
    margin-bottom: 8px;
    color: #34495e;
    font-weight: 600;
    font-size: 1.0rem;
}

.btn-register {
    background: linear-gradient(45deg, #000080, #3498db);
    color: white;
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-register:hover {
    background: linear-gradient(45deg, #3498db, #000080);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.login-link {
    color: #7f8c8d;
    font-size: 1.0rem;
    transition: color 0.3s ease;
}

.login-link a {
    color: #3498db;
    text-decoration: none;
    margin-left: 5px;
}

.login-link a:hover {
    text-decoration: underline;
}

@media (max-width: 480px) {
    .register-container {
        padding: 30px 20px;
        margin: 15px;
    }
    
    .text-register {
        font-size: 1.8rem;
    }
}
.navy-toast {
      background-color: #001f3f !important; /* Màu xanh navy */
      color: white !important;
      font-size: 16px !important;
      font-weight: 500 !important;
      padding: 15px 20px !important;
      border-radius: 8px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      width: 380px !important;
      min-height: 80px !important;
    }
    
    .navy-toast-progress {
      background: rgba(255, 255, 255, 0.7) !important;
      height: 4px !important;
    }