// Variables
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$accent-color: #ff5a5a;
$transition: all 0.3s ease;
$border-radius: 8px;
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
.homepage-productscontainer {
  margin: 0;
  padding: 40px 20px;
  max-height: 80vh;
  min-height: 80vh;
  flex-wrap: wrap;
  overflow-y: scroll;
  display: flex;
  gap: 40px;
  justify-content: center;
  background-color: #f8f9fa;
  scrollbar-width: none;
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 80px;

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 6px solid #f3f3f3;
      border-top: 6px solid $navy-dark;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 30px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    p {
      color: $navy-dark;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .no-products {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 80px;

    i {
      font-size: 64px;
      color: $dark-gray;
      margin-bottom: 30px;
    }

    p {
      color: $dark-gray;
      font-size: 20px;
    }
  }

  // Make products larger
  .product-container {
    width: 280px;
    margin: 0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    // .product-image-container {
    //   height: 280px;

    //   .product-image {
    //     height: 100%;
    //     object-fit: cover;
    //   }
    // }

    .product-info-container {
      padding: 20px;

      .product-name {
        font-size: 18px;
        margin-bottom: 12px;
        max-height: 50px;
      }

      .product-shop {
        margin: 10px 0;
      }

      .product-price {
        margin: 15px 0;

        .original-price {
          font-size: 16px;
        }

        .sale-price {
          font-size: 20px;
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.homepage-container {
  display: flex;
  flex-direction: column;
}
.homepage-dash {
  display: block;
  margin-top: 20px;
  max-height: 2vh;
  height: 100%;
  width: 50%;
  margin: 0px auto;
  background: $navy-medium;
  border: 0;
  z-index: 1;
}
.homepage-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2vh 0;
  gap: 10px;

  button {
    max-width: 5vh;
    width: 100%;
    height: 100%;
    max-height: 5vh;
    border-radius: 8px;
    border: 1px solid #e4e8f0;
    background-color: #ffffff;
    color: #1a3a6c;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f7f9fc;
      border-color: #3a5998;
      transform: translateY(-2px);
      box-shadow: $box-shadow;
    }

    &.active {
      background: linear-gradient(135deg, #1a3a6c, #2c4272, #3a5998);
      background-size: 200% 200%;
      animation: gradientMove 8s ease infinite;
      color: #ffffff;
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(26, 58, 108, 0.3);
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
        background-color: #ffffff;
        border-color: #e4e8f0;
      }
    }
  }
}
.homepage-pagination-now {
  border: 2px solid $navy-dark !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Media Queries */
@media (max-width: 768px) {
  .homepage-pagination {
    gap: 8px;

    button {
      width: 35px;
      height: 35px;
      font-size: 13px;
    }
  }
}

@media (max-width: 576px) {
  .homepage-pagination {
    gap: 6px;
    button {
      width: 32px;
      height: 32px;
      font-size: 12px;
    }
  }
}
