// Variables
$navy-dark: #1a3a6c;
$navy-medium: #2c4272;
$navy-light: #3a5998;
$white: #ffffff;
$light-gray: #f7f9fc;
$medium-gray: #e4e8f0;
$dark-gray: #8492a6;
$green: #4caf50;
$red: #e53935;
$orange: #ff9800;
$transition: all 0.3s ease;
$border-radius: 8px;

// Mixins
@mixin navy-gradient {
  background: linear-gradient(135deg, $navy-dark, $navy-medium, $navy-light);
  background-size: 200% 200%;
  animation: gradientMove 8s ease infinite;
}

@mixin star-effect {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        1px 1px at 10% 20%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%
      ),
      radial-gradient(
        1.2px 1.2px at 70% 30%,
        rgba(255, 255, 255, 0.5) 50%,
        transparent 100%
      ),
      radial-gradient(
        1px 1px at 30% 70%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%
      ),
      radial-gradient(
        1.3px 1.3px at 80% 60%,
        rgba(255, 255, 255, 0.5) 50%,
        transparent 100%
      ),
      radial-gradient(
        1.1px 1.1px at 50% 40%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%
      );
    pointer-events: none;
    opacity: 0.3;
    animation: twinkle 5s infinite alternate;
  }
}

@mixin button-style {
  padding: 8px 14px;
  border: none;
  outline: none;
  cursor: pointer;
  border-radius: $border-radius;
  transition: $transition;
  font-weight: 600;
}

// Main Styles
.oneOrder {
  margin: 0 20px 30px;
  background-color: $white;
  border-radius: $border-radius;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 25px;
  position: relative;
  overflow: hidden;

  // Navy gradient strip at top
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    @include navy-gradient;
    @include star-effect;
  }

  .oneOrder-title {
    text-align: center;
    margin: 5px 0 25px;
    text-transform: uppercase;
    font-size: 22px;
    font-weight: 600;
    color: $navy-dark;
    position: relative;
    padding-bottom: 12px;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 3px;
      width: 70px;
      @include navy-gradient;
      border-radius: 3px;
    }
  }

  hr {
    height: 2px;
    background: $medium-gray;
    border: 0;
    margin: 15px 0;
  }

  .oneOrder-format-main {
    display: grid;
    grid-template-columns: 0.5fr 2fr 1fr 1fr 1fr 1fr;
    align-items: center;
    gap: 75px;
    padding: 20px 0px;
    color: $navy-dark;
    font-size: 16px;
    font-weight: 600;

    p {
      transition: $transition;
    }
  }

  .oneOrder-format {
    font-size: 15px;
    font-weight: 500;
    color: $dark-gray;

    &.oneOrder-format-main {
      background-color: rgba($light-gray, 0.7);
      border-radius: $border-radius;
      padding: 15px;
      margin: 10px 0;
      transition: $transition;

      &:hover {
        background-color: rgba($navy-light, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }

  .oneOrder-product-icon {
    height: 62px;
    width: 62px;
    border-radius: $border-radius;
    object-fit: cover;
    border: 2px solid transparent;
    transition: $transition;

    &:hover {
      transform: scale(1.1);
      border-color: $navy-light;
      box-shadow: 0 0 8px rgba($navy-light, 0.5);
    }
  }

  .oneOrder-remove-icon {
    width: 15px;
    margin: 0px 40px;
    cursor: pointer;
    transition: $transition;

    &:hover {
      transform: scale(1.2) rotate(5deg);
      color: $red;
    }
  }

  .oneOrder-quantity {
    width: 64px;
    height: 50px;
    border: 2px solid $medium-gray;
    border-radius: $border-radius;
    background: $white;
    cursor: default;
    color: $navy-dark;
    font-weight: 600;
    text-align: center;
    transition: $transition;

    &:hover {
      border-color: $navy-light;
      box-shadow: 0 0 0 3px rgba($navy-light, 0.15);
    }
  }

  .oneOrder-down {
    display: flex;
    margin: 80px 0px 40px;
  }

  .oneOrder-total {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-right: 200px;
    gap: 30px;
    background-color: $light-gray;
    padding: 25px;
    border-radius: $border-radius;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;

    // Subtle gradient at top
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      @include navy-gradient;
    }

    h1 {
      color: $navy-dark;
      font-size: 24px;
      margin-bottom: 15px;
      position: relative;
      display: inline-block;

      &::after {
        content: "";
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 40px;
        height: 2px;
        @include navy-gradient;
        border-radius: 2px;
      }
    }

    .oneOrder-total-item {
      display: flex;
      justify-content: space-between;
      padding: 15px 0px;
      border-bottom: 1px solid $medium-gray;
      transition: $transition;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: rgba($white, 0.6);
      }

      p:first-child {
        color: $dark-gray;
        font-weight: 500;
      }

      p:last-child {
        color: $navy-dark;
        font-weight: 600;
      }

      h3 {
        color: $navy-dark;
        font-size: 20px;
      }
    }

    button {
      @include button-style;
      @include navy-gradient;
      color: $white;
      width: 262px;
      height: 58px;
      font-size: 16px;
      align-self: flex-start;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba($navy-dark, 0.3);
      }
    }
  }

  .oneOrder-promocode {
    flex: 1;
    font-size: 16px;
    font-weight: 500;

    p {
      color: $dark-gray;
      margin-bottom: 10px;
    }
  }

  .oneOrder-promobox {
    width: 504px;
    margin-top: 15px;
    padding-left: 20px;
    height: 58px;
    background: $light-gray;
    border-radius: $border-radius;
    display: flex;
    align-items: center;
    transition: $transition;

    &:focus-within {
      box-shadow: 0 0 0 3px rgba($navy-light, 0.15);
    }

    input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      width: 330px;
      height: 50px;
      color: $navy-dark;
    }

    button {
      @include button-style;
      @include navy-gradient;
      width: 170px;
      height: 58px;
      font-size: 16px;
      color: $white;
      border-radius: 0 $border-radius $border-radius 0;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba($navy-dark, 0.3);
      }
    }
  }

  // Media Queries
  @media (max-width: 1280px) {
    margin: 30px 20px;
    padding: 20px;

    .oneOrder-format-main {
      grid-template-columns: 0.5fr 3fr 0.5fr 0.5fr 0fr;
      gap: 20px;
      padding: 15px 0px;
      font-size: 15px;
    }

    .oneOrder-product-icon {
      height: 50px;
    }

    .oneOrder-remove-icon {
      margin: auto;
    }

    .oneOrder-quantity {
      width: 40px;
      height: 30px;
    }

    .oneOrder-down {
      margin: 60px 0px 30px;
      flex-direction: column;
      gap: 60px;
    }

    .oneOrder-total {
      margin: 0;
      padding: 20px;
    }

    .oneOrder-total button {
      max-width: 200px;
      height: 45px;
      font-size: 13px;
    }

    .oneOrder-promobox {
      width: auto;
      max-width: 500px;
    }

    .oneOrder-promobox input {
      width: 100%;
    }

    .oneOrder-promobox button {
      width: 120px;
      margin-left: -125px;
    }
  }

  @media (max-width: 500px) {
    .oneOrder-format-main {
      display: none;
      grid-template-columns: 0.5fr 3fr 0.5fr;
      gap: 10px;
    }

    .oneOrder-format {
      display: grid;
    }
  }
}

// Animations
@keyframes twinkle {
  0% {
    opacity: 0.15;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.15;
  }
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
